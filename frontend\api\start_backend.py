#!/usr/bin/env python3
"""
Startup script for the Fake News Detection Backend
This script will install dependencies and start the Flask server
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    return True

def start_server():
    """Start the Flask development server"""
    print("Starting Flask development server...")
    print("🚀 Server will be available at: http://localhost:5000")
    print("📊 API endpoints:")
    print("   - POST /api/register - User registration")
    print("   - POST /api/login - User login")
    print("   - POST /api/detect - Fake news detection")
    print("   - GET /api/history - Detection history")
    print("   - GET /api/profile - User profile")
    print("   - GET /api/results/<id> - Specific result")
    print("\n" + "="*50)
    
    try:
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    print("🔍 Fake News Detection Backend Startup")
    print("="*40)
    
    # Check if we're in the right directory
    if not os.path.exists("app.py"):
        print("❌ Error: app.py not found. Please run this script from the api directory.")
        sys.exit(1)
    
    # Install dependencies
    if not install_requirements():
        sys.exit(1)
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
