
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import os
import json
import time
import re
import hashlib
from datetime import datetime
from urllib.parse import urlparse
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
from functools import wraps

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')
PERPLEXITY_API_KEY = os.environ.get("PERPLEXITY_API_KEY", "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")

# Database setup
def init_db():
    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Detection results table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS detection_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            text_content TEXT NOT NULL,
            url TEXT,
            result TEXT NOT NULL,
            confidence REAL NOT NULL,
            analysis TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Sessions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sessions (
            id TEXT PRIMARY KEY,
            user_id INTEGER,
            title TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Conversations table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversations (
            id TEXT PRIMARY KEY,
            session_id TEXT NOT NULL,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            citations TEXT,
            parent_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES sessions (id)
        )
    ''')

    conn.commit()
    conn.close()

# Initialize database
init_db()

# JWT token decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401

        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
        except:
            return jsonify({'error': 'Token is invalid'}), 401

        return f(current_user_id, *args, **kwargs)
    return decorated

# Authentication routes
@app.route('/api/register', methods=['POST'])
def register():
    data = request.json
    username = data.get('username')
    email = data.get('email')
    password = data.get('password')

    if not username or not email or not password:
        return jsonify({'error': 'Username, email, and password are required'}), 400

    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    try:
        # Check if user already exists
        cursor.execute('SELECT id FROM users WHERE username = ? OR email = ?', (username, email))
        if cursor.fetchone():
            return jsonify({'error': 'User already exists'}), 400

        # Create new user
        password_hash = generate_password_hash(password)
        cursor.execute('INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
                      (username, email, password_hash))
        conn.commit()

        return jsonify({'message': 'User created successfully'}), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'error': 'Username and password are required'}), 400

    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT id, username, email, password_hash FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()

        if not user or not check_password_hash(user[3], password):
            return jsonify({'error': 'Invalid credentials'}), 401

        # Generate JWT token
        token = jwt.encode({
            'user_id': user[0],
            'username': user[1],
            'exp': datetime.utcnow().timestamp() + 86400  # 24 hours
        }, app.config['SECRET_KEY'], algorithm='HS256')

        return jsonify({
            'token': token,
            'user': {
                'id': user[0],
                'username': user[1],
                'email': user[2]
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Fake news detection function
def detect_fake_news(text):
    """
    Enhanced fake news detection based on common patterns and keywords.
    In production, you would use a trained ML model.
    """
    # Convert to lowercase for analysis
    text_lower = text.lower()

    # Suspicious patterns and keywords (more comprehensive)
    fake_indicators = [
        'breaking:', 'urgent:', 'shocking:', 'unbelievable:', 'you won\'t believe',
        'doctors hate', 'this one trick', 'click here', 'must read', 'amazing discovery',
        'exclusive:', 'leaked:', 'secret:', 'hidden truth', 'they don\'t want you to know',
        'miracle cure', 'instant results', 'guaranteed', 'revolutionary breakthrough',
        'scientists baffled', 'government cover-up', 'big pharma', 'conspiracy',
        'fake news media', 'mainstream media lies', 'wake up people', 'share before deleted'
    ]

    reliable_indicators = [
        'according to', 'research shows', 'study finds', 'data indicates',
        'experts say', 'official statement', 'peer-reviewed', 'published in',
        'university study', 'clinical trial', 'scientific evidence', 'researchers found',
        'journal of', 'institute', 'department of', 'professor', 'dr.', 'phd'
    ]

    # Count indicators with weighted scoring
    fake_score = 0
    reliable_score = 0

    for indicator in fake_indicators:
        if indicator in text_lower:
            # Weight certain indicators more heavily
            if indicator in ['breaking:', 'shocking:', 'you won\'t believe', 'doctors hate', 'this one trick']:
                fake_score += 2
            else:
                fake_score += 1

    for indicator in reliable_indicators:
        if indicator in text_lower:
            # Weight academic indicators more heavily
            if indicator in ['peer-reviewed', 'published in', 'clinical trial', 'university study']:
                reliable_score += 2
            else:
                reliable_score += 1

    # Check for excessive punctuation or caps
    excessive_caps = len(re.findall(r'[A-Z]{3,}', text)) > 2
    excessive_punctuation = len(re.findall(r'[!?]{2,}', text)) > 1
    excessive_exclamation = text.count('!') > 3

    if excessive_caps:
        fake_score += 2
    if excessive_punctuation:
        fake_score += 2
    if excessive_exclamation:
        fake_score += 1

    # Calculate result and confidence
    if fake_score == 0 and reliable_score == 0:
        # No clear indicators - check for other patterns
        if len(text.split()) < 5:
            confidence = 0.3  # Very short text, low confidence
            result = "UNCERTAIN"
        else:
            confidence = 0.5  # Neutral
            result = "UNCERTAIN"
    else:
        # Calculate confidence based on the stronger signal
        total_indicators = fake_score + reliable_score

        if fake_score > reliable_score:
            result = "LIKELY_FAKE"
            # Higher fake score = higher confidence it's fake
            confidence = min(0.95, 0.6 + (fake_score / (fake_score + reliable_score)) * 0.35)
        elif reliable_score > fake_score:
            result = "LIKELY_REAL"
            # Higher reliable score = higher confidence it's real
            confidence = min(0.95, 0.6 + (reliable_score / (fake_score + reliable_score)) * 0.35)
        else:
            result = "UNCERTAIN"
            confidence = 0.5

    # Analysis text
    analysis_parts = []
    if fake_score > 0:
        analysis_parts.append(f"Found {fake_score} suspicious indicators")
    if reliable_score > 0:
        analysis_parts.append(f"Found {reliable_score} reliability indicators")
    if excessive_caps:
        analysis_parts.append("Excessive use of capital letters detected")
    if excessive_punctuation:
        analysis_parts.append("Excessive punctuation detected")
    if excessive_exclamation:
        analysis_parts.append("Excessive exclamation marks detected")

    analysis = ". ".join(analysis_parts) if analysis_parts else "No clear indicators found"

    return result, confidence, analysis

# Fake news detection endpoint
@app.route('/api/detect', methods=['POST'])
def detect_news():
    data = request.json
    text = data.get('text')
    url = data.get('url')

    if not text and not url:
        return jsonify({'error': 'Either text or URL is required'}), 400

    # If URL is provided, try to extract text (simplified)
    if url and not text:
        try:
            response = requests.get(url, timeout=10)
            # Simple text extraction (in production, use proper HTML parsing)
            text = response.text[:5000]  # Limit text length
        except:
            return jsonify({'error': 'Could not fetch content from URL'}), 400

    if not text:
        return jsonify({'error': 'No text content to analyze'}), 400

    # Perform detection
    result, confidence, analysis = detect_fake_news(text)

    # Get user ID if authenticated
    user_id = None
    token = request.headers.get('Authorization')
    if token:
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data_token = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            user_id = data_token['user_id']
        except:
            pass  # Continue without user ID if token is invalid

    # Save result to database
    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    try:
        cursor.execute('''
            INSERT INTO detection_results (user_id, text_content, url, result, confidence, analysis)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (user_id, text[:1000], url, result, confidence, analysis))
        conn.commit()
        result_id = cursor.lastrowid
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

    return jsonify({
        'id': result_id,
        'result': result,
        'confidence': round(confidence, 2),
        'analysis': analysis,
        'text_preview': text[:200] + '...' if len(text) > 200 else text
    })

# Get detection history
@app.route('/api/history', methods=['GET'])
@token_required
def get_history(current_user_id):
    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT id, text_content, url, result, confidence, analysis, created_at
            FROM detection_results
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 50
        ''', (current_user_id,))

        results = []
        for row in cursor.fetchall():
            results.append({
                'id': row[0],
                'text_preview': row[1][:100] + '...' if len(row[1]) > 100 else row[1],
                'url': row[2],
                'result': row[3],
                'confidence': row[4],
                'analysis': row[5],
                'created_at': row[6]
            })

        return jsonify(results)

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Get specific detection result
@app.route('/api/results/<int:result_id>', methods=['GET'])
def get_result(result_id):
    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT id, text_content, url, result, confidence, analysis, created_at
            FROM detection_results
            WHERE id = ?
        ''', (result_id,))

        row = cursor.fetchone()
        if not row:
            return jsonify({'error': 'Result not found'}), 404

        return jsonify({
            'id': row[0],
            'text_content': row[1],
            'url': row[2],
            'result': row[3],
            'confidence': row[4],
            'analysis': row[5],
            'created_at': row[6]
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# User profile endpoint
@app.route('/api/profile', methods=['GET'])
@token_required
def get_profile(current_user_id):
    conn = sqlite3.connect('fake_news_detector.db')
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT username, email, created_at FROM users WHERE id = ?', (current_user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get detection count
        cursor.execute('SELECT COUNT(*) FROM detection_results WHERE user_id = ?', (current_user_id,))
        detection_count = cursor.fetchone()[0]

        return jsonify({
            'username': user[0],
            'email': user[1],
            'created_at': user[2],
            'detection_count': detection_count
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    app.run(debug=True, port=5000)
