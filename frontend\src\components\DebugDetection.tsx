import React, { useState } from 'react';
import { detectionAPI } from '../api/perplexity';

const DebugDetection: React.FC = () => {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testFakeNews = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    const fakeText = "BREAKING: You won't believe this shocking discovery! Doctors hate this one trick!";
    
    try {
      console.log('Testing fake news:', fakeText);
      const response = await detectionAPI.detectNews(fakeText);
      console.log('Response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testRealNews = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    const realText = "According to a peer-reviewed study published in Nature, researchers found evidence.";
    
    try {
      console.log('Testing real news:', realText);
      const response = await detectionAPI.detectNews(realText);
      console.log('Response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Debug Detection API</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testFakeNews} 
          disabled={loading}
          style={{ marginRight: '10px', padding: '10px 20px' }}
        >
          Test Fake News
        </button>
        <button 
          onClick={testRealNews} 
          disabled={loading}
          style={{ padding: '10px 20px' }}
        >
          Test Real News
        </button>
      </div>

      {loading && <p>Loading...</p>}
      
      {error && (
        <div style={{ color: 'red', padding: '10px', border: '1px solid red', marginBottom: '20px' }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {result && (
        <div style={{ padding: '20px', border: '1px solid #ccc', backgroundColor: '#f9f9f9' }}>
          <h3>Result:</h3>
          <p><strong>Classification:</strong> {result.result}</p>
          <p><strong>Confidence:</strong> {(result.confidence * 100).toFixed(1)}%</p>
          <p><strong>Analysis:</strong> {result.analysis}</p>
          <p><strong>ID:</strong> {result.id}</p>
          <p><strong>Text Preview:</strong> {result.text_preview}</p>
          
          <h4>Raw Response:</h4>
          <pre style={{ backgroundColor: '#eee', padding: '10px', overflow: 'auto' }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default DebugDetection;
