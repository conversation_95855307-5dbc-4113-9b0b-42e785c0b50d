@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 inline-flex items-center justify-center gap-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-4 focus:ring-primary-200;
  }
  
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-4 focus:ring-secondary-200;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white hover:bg-gray-50 focus:ring-4 focus:ring-gray-100;
  }

  .input-field {
    @apply w-full rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-xl shadow-md p-6 transition-all duration-200;
  }
  
  .card-hover {
    @apply hover:shadow-lg;
  }
}

.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}