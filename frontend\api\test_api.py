#!/usr/bin/env python3
"""
Test script for the Fake News Detection API
This script tests all the main endpoints to ensure they're working correctly.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000/api"

def test_endpoint(method, endpoint, data=None, headers=None, expected_status=200):
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        if response.status_code == expected_status:
            print(f"✅ {method} {endpoint} - Status: {response.status_code}")
            return response.json() if response.content else True
        else:
            print(f"❌ {method} {endpoint} - Expected: {expected_status}, Got: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    
    except requests.exceptions.ConnectionError:
        print(f"❌ {method} {endpoint} - Connection failed. Is the server running?")
        return False
    except Exception as e:
        print(f"❌ {method} {endpoint} - Error: {e}")
        return False

def main():
    print("🔍 Testing Fake News Detection API")
    print("=" * 50)
    
    # Test user registration
    print("\n📝 Testing User Registration...")
    test_username = f"testuser_{int(time.time())}"
    test_email = f"test_{int(time.time())}@example.com"
    
    register_data = {
        "username": test_username,
        "email": test_email,
        "password": "testpassword123"
    }
    
    register_result = test_endpoint("POST", "/register", register_data, expected_status=201)
    
    # Test user login
    print("\n🔐 Testing User Login...")
    login_data = {
        "username": test_username,
        "password": "testpassword123"
    }
    
    login_result = test_endpoint("POST", "/login", login_data)
    
    token = None
    if login_result and isinstance(login_result, dict) and "token" in login_result:
        token = login_result["token"]
        print(f"   Token received: {token[:20]}...")
    
    # Test fake news detection (without auth)
    print("\n🔍 Testing Fake News Detection (No Auth)...")
    detection_data = {
        "text": "BREAKING: Scientists discover shocking truth about water! You won't believe what they found!"
    }
    
    detection_result = test_endpoint("POST", "/detect", detection_data)
    
    result_id = None
    if detection_result and isinstance(detection_result, dict) and "id" in detection_result:
        result_id = detection_result["id"]
        print(f"   Detection result: {detection_result['result']} (confidence: {detection_result['confidence']})")
    
    # Test getting specific result
    if result_id:
        print("\n📊 Testing Get Specific Result...")
        test_endpoint("GET", f"/results/{result_id}")
    
    # Test authenticated endpoints
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        
        print("\n👤 Testing User Profile...")
        test_endpoint("GET", "/profile", headers=headers)
        
        print("\n📈 Testing Detection History...")
        test_endpoint("GET", "/history", headers=headers)
        
        print("\n🔍 Testing Fake News Detection (With Auth)...")
        detection_data_auth = {
            "text": "According to peer-reviewed research published in Nature, scientists have made a significant discovery."
        }
        test_endpoint("POST", "/detect", detection_data_auth, headers=headers)
    
    # Test error cases
    print("\n❌ Testing Error Cases...")
    
    # Invalid login
    test_endpoint("POST", "/login", {"username": "invalid", "password": "wrong"}, expected_status=401)
    
    # Missing data for detection
    test_endpoint("POST", "/detect", {}, expected_status=400)
    
    # Invalid token
    invalid_headers = {"Authorization": "Bearer invalid_token"}
    test_endpoint("GET", "/profile", headers=invalid_headers, expected_status=401)
    
    print("\n" + "=" * 50)
    print("🎉 API Testing Complete!")
    print("\nIf all tests passed, your backend is working correctly!")
    print("You can now connect your frontend to the backend.")

if __name__ == "__main__":
    main()
