# Fake News Detection API

This is the Flask backend for the Fake News Detection System, providing authentication, fake news detection, and user management features.

## Quick Start

### Option 1: Use the startup scripts (Recommended)

**Windows:**
```bash
cd project/api
start_backend.bat
```

**Mac/Linux/Windows with Python:**
```bash
cd project/api
python start_backend.py
```

### Option 2: Manual setup

1. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

2. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Mac/Linux: `source venv/bin/activate`

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the Flask app:
   ```bash
   python app.py
   ```

The API will be available at `http://localhost:5000`.

## Features

- ✅ User authentication (register/login) with JWT tokens
- ✅ Fake news detection using pattern analysis
- ✅ SQLite database for data persistence
- ✅ Detection history tracking
- ✅ User profiles and statistics
- ✅ CORS enabled for frontend integration

## API Endpoints

### Authentication
- `POST /api/register` - User registration
  - Request: `{ "username": "string", "email": "string", "password": "string" }`
  - Response: `{ "message": "User created successfully" }`

- `POST /api/login` - User login
  - Request: `{ "username": "string", "password": "string" }`
  - Response: `{ "token": "jwt_token", "user": { "id": 1, "username": "user", "email": "<EMAIL>" } }`

### Fake News Detection
- `POST /api/detect` - Detect fake news
  - Request: `{ "text": "string" }` or `{ "url": "string" }`
  - Response: `{ "id": 1, "result": "LIKELY_FAKE|LIKELY_REAL|UNCERTAIN", "confidence": 0.85, "analysis": "string", "text_preview": "string" }`

- `GET /api/results/<id>` - Get specific detection result
  - Response: Full detection result with complete text

### User Data (Requires Authentication)
- `GET /api/profile` - Get user profile
  - Headers: `Authorization: Bearer <token>`
  - Response: `{ "username": "string", "email": "string", "created_at": "timestamp", "detection_count": 5 }`

- `GET /api/history` - Get detection history
  - Headers: `Authorization: Bearer <token>`
  - Response: Array of detection results

## Database Schema

The API uses SQLite with the following tables:
- `users` - User accounts
- `detection_results` - Fake news detection results
- `sessions` - Chat sessions (for future use)
- `conversations` - Chat conversations (for future use)

## Detection Algorithm

The current fake news detection uses pattern analysis based on:
- Suspicious keywords and phrases
- Reliability indicators
- Excessive capitalization
- Excessive punctuation
- Content structure analysis

**Note:** In production, this should be replaced with a trained machine learning model.